# PyInstaller 打包 Python 应用程序完整流程

本文档详细介绍如何使用 PyInstaller 将 Python 应用程序打包成可执行文件的完整流程。

## 目录
1. [环境准备](#1-环境准备)
2. [创建虚拟环境](#2-创建虚拟环境)
3. [安装依赖](#3-安装依赖)
4. [安装PyInstaller](#4-安装pyinstaller)
5. [打包应用程序](#5-打包应用程序)
6. [测试可执行文件](#6-测试可执行文件)
7. [常见问题解决](#7-常见问题解决)
8. [高级配置](#8-高级配置)

## 1. 环境准备

### 1.1 确保Python已安装
```bash
python --version
# 或
python3 --version
```

### 1.2 确保pip已安装
```bash
pip --version
# 或
pip3 --version
```

## 2. 创建虚拟环境

### 2.1 使用venv创建虚拟环境
```bash
# 在项目目录下创建虚拟环境
python -m venv venv

# 或者指定Python版本
python3 -m venv venv
```

### 2.2 激活虚拟环境

**Windows:**
```bash
# 命令提示符
venv\Scripts\activate

# PowerShell
venv\Scripts\Activate.ps1

# Git Bash
source venv/Scripts/activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 2.3 验证虚拟环境
```bash
# 确认使用的是虚拟环境中的Python
which python
# 或
where python
```

## 3. 安装依赖

### 3.1 升级pip
```bash
python -m pip install --upgrade pip
```

### 3.2 从requirements.txt安装依赖
```bash
pip install -r requirements.txt
```

### 3.3 验证依赖安装
```bash
pip list
```

## 4. 安装PyInstaller

### 4.1 安装PyInstaller
```bash
pip install pyinstaller
```

### 4.2 验证PyInstaller安装
```bash
pyinstaller --version
```

## 5. 打包应用程序

### 5.1 基本打包命令
```bash
# 最简单的打包方式
pyinstaller your_script.py

# 针对本项目
pyinstaller "打包教学\LJ_Zhang_2D_pattern_V2.py"
```

### 5.2 常用打包选项
```bash
# 打包成单个可执行文件
pyinstaller --onefile your_script.py

# 不显示控制台窗口（适用于GUI应用）
pyinstaller --windowed your_script.py

# 组合使用
pyinstaller --onefile --windowed your_script.py

# 指定图标
pyinstaller --onefile --windowed --icon=app.ico your_script.py

# 指定输出目录
pyinstaller --onefile --windowed --distpath ./dist your_script.py
```

### 5.3 针对本项目的推荐命令
```bash
# 由于是PyQt5 GUI应用，推荐使用以下命令
pyinstaller --onefile --windowed --name="TwoDpattern" "打包教学\LJ_Zhang_2D_pattern_V2.py"
```

### 5.4 处理特殊依赖
```bash
# 如果有隐藏导入
pyinstaller --onefile --windowed --hidden-import=module_name your_script.py

# 添加数据文件
pyinstaller --onefile --windowed --add-data "data_file;." your_script.py

# Windows下使用分号，Linux/macOS使用冒号
# Windows: --add-data "source;dest"
# Linux/macOS: --add-data "source:dest"
```

## 6. 测试可执行文件

### 6.1 查找生成的文件
```bash
# 可执行文件通常在以下位置
ls dist/
# 或
dir dist\
```

### 6.2 运行可执行文件
```bash
# Windows
dist\your_script.exe

# Linux/macOS
./dist/your_script
```

### 6.3 测试功能
- 测试所有主要功能
- 检查文件读写操作
- 验证GUI界面显示
- 测试错误处理

## 7. 常见问题解决

### 7.1 模块导入错误
```bash
# 添加隐藏导入
pyinstaller --hidden-import=missing_module your_script.py
```

### 7.2 数据文件缺失
```bash
# 添加数据文件
pyinstaller --add-data "data_folder;data_folder" your_script.py
```

### 7.3 PyQt5相关问题
```bash
# 确保包含PyQt5插件
pyinstaller --hidden-import=PyQt5.sip your_script.py
```

### 7.4 文件过大
```bash
# 使用UPX压缩（需要先安装UPX）
pyinstaller --onefile --upx-dir=/path/to/upx your_script.py
```

### 7.5 启动慢
```bash
# 不使用--onefile，保持文件夹结构
pyinstaller --windowed your_script.py
```

## 8. 高级配置

### 8.1 使用spec文件
```bash
# 生成spec文件
pyi-makespec --onefile --windowed your_script.py

# 编辑spec文件后重新打包
pyinstaller your_script.spec
```

### 8.2 spec文件示例
```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['LJ_Zhang_2D_pattern_V2.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['PyQt5.sip'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TwoDpattern',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app.ico'
)
```

### 8.3 清理和重新打包
```bash
# 清理之前的构建文件
rm -rf build/ dist/ *.spec
# Windows: rmdir /s build dist & del *.spec

# 重新打包
pyinstaller --onefile --windowed your_script.py
```

## 9. 最佳实践

### 9.1 项目结构建议
```
project/
├── src/
│   └── main.py
├── data/
├── icons/
├── requirements.txt
├── build_script.bat (或 build_script.sh)
└── README.md
```

### 9.2 构建脚本示例
**Windows (build.bat):**
```batch
@echo off
echo 开始打包...
pyinstaller --onefile --windowed --name="TwoDpattern" --icon=icon.ico src\main.py
echo 打包完成！
pause
```

**Linux/macOS (build.sh):**
```bash
#!/bin/bash
echo "开始打包..."
pyinstaller --onefile --windowed --name="TwoDpattern" src/main.py
echo "打包完成！"
```

### 9.3 版本控制
- 将 `build/`, `dist/`, `*.spec` 添加到 `.gitignore`
- 保留 `requirements.txt` 和构建脚本
- 记录打包命令和特殊配置

## 10. 故障排除

### 10.1 调试模式
```bash
# 启用调试模式查看详细信息
pyinstaller --debug=all your_script.py
```

### 10.2 日志分析
```bash
# 查看构建日志
pyinstaller --log-level=DEBUG your_script.py
```

### 10.3 依赖分析
```bash
# 分析依赖关系
pyi-archive_viewer dist/your_script.exe
```

---

**注意事项：**
- 确保在目标操作系统上测试可执行文件
- 大型应用建议使用文件夹模式而非单文件模式
- 定期更新PyInstaller版本以获得最佳兼容性
- 考虑使用虚拟机测试在干净环境中的运行情况
