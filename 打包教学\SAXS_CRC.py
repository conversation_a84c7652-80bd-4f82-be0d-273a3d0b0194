import numpy as np
import os
import cupy as cp
import matplotlib.pyplot as plt
from scipy.integrate import simpson
from scipy.interpolate import CubicSpline
from PIL import Image
import shutil
from scipy.stats import binned_statistic

# %% create array
def arange(start, step, stop):
    num = int(np.round((stop - start) / step)) + 1  # 自动计算点数
    return np.linspace(start, stop, num).reshape(-1, 1)


# %% get beta distribution
def get_beta(Rmin, Rmax, a, b, number):

    Y = np.zeros((number, 3))

    Y[:, 0] = np.linspace(Rmin, Rmax, number)
    Y[:, 1] = (((Y[:, 0]-Rmin)/(Rmax-Rmin))**(a-1) *
               ((Rmax-Y[:, 0])/(Rmax-Rmin))**(b-1))

    Y1 = simpson(Y[:, 1], Y[:, 0])
    Y[:, 1] = Y[:, 1] / Y1

    Y[:, 2] = 4*(np.pi)*Y[:, 0]**3/3 * Y[:, 1]
    Y2 = simpson(Y[:, 2], Y[:, 0])
    Y[:, 2] = Y[:, 2] / Y2
    Y[0, 0] = 1e-6
    return Y


# %% get Intensity of sphere with volume fraction
def Sphere_I_V(q, Y):
    q[0] = 1e-6
    Y[0, 0] = 1e-6

    U = q.reshape(-1, 1) * Y[0, :].reshape(-1, 1).T
    F = (3 / U**3) * (np.sin(U)-U * np.cos(U))
    I0 = F * F * Y[1, :] * (4 * np.pi * Y[0, :]**3 / 3)
    I1 = I0.mean(axis=1, keepdims=True)
    I1 = np.column_stack((q.reshape(-1, 1), I1, np.log(I1)))
    return I1


# %% change the stepwidth of scattering data
def change_stepwidth(I3, stepwidth):
    I3 = np.array(I3)
    number = int(np.round((I3[:, 0].max() - 0) / stepwidth)) + 1
    q = np.linspace(0, I3[:, 0].max(), number).reshape(-1, 1)
    q[0, 0] = 1e-6
    min_q, max_q = I3[:, 0].min(), I3[:, 0].max()
    k1, k2 = np.searchsorted(q[:, 0], [min_q, max_q], side='right')-1
    linshi = CubicSpline(I3[:, 0], I3[:, 2])(q[k1:k2+1, 0]).reshape(-1, 1)
    return np.column_stack((q[k1:k2+1, :], np.exp(linshi), linshi))


# %% auto_adjust_axes坐标轴自动调整功能
def auto_adjust_axes(ax=None, margin=0.05):
    """自动调整坐标轴范围适配所有数据"""
    ax = ax or plt.gca()
    ax.relim()  # 重新计算数据限制
    ax.autoscale_view()  # 基于新数据自动调整视图
    ax.figure.canvas.draw()  # 立即刷新图表

    # 手动添加边距
    x_min, x_max = ax.get_xlim()
    y_min, y_max = ax.get_ylim()
    ax.set_xlim(x_min - (x_max - x_min) * margin,
                x_max + (x_max - x_min) * margin)
    ax.set_ylim(y_min - (y_max - y_min) * margin,
                y_max + (y_max - y_min) * margin)


# %% porod_correct
def porod_correct(I_input):
    plt.close('all')
    C = 4 * np.log(I_input[:, 0]) + np.log(I_input[:, 1])
    E = arange(0, 0.02, 100)
    C = np.tile(C[:, np.newaxis], (1, E.shape[0]))
    # np.newaxis将一维数组转换为列向量（二维数组）

    D = I_input[:, 0] ** 2
    D = np.tile(D[:, np.newaxis], (1, E.shape[0]))

    C_corrected = C - D * E.T
    remove_num = int(np.floor(C_corrected.shape[0] * 4 / 5))
    C_remaining = C_corrected[remove_num:, :]
    # 只保留C_corrected的尾巴部分

    std_devs = np.std(C_remaining, axis=0, ddof=0)  # ddof 用来计算总体标准差
    k = E[np.argmin(std_devs)]

    # 绘图数据准备
    A = np.column_stack((I_input[:, 0] ** 2, 4 * np.log(I_input[:, 0])
                         + np.log(I_input[:, 1])))
    A = np.column_stack((A, A[:, 1] - k * A[:, 0]))

    plt.ion()  # 启用交互模式
    F1 = plt.figure(1, figsize=(10, 10))
    Line1 = F1.add_subplot(111)
    Line1.set_xlabel(r'$q^2\ (1/\mathrm{nm})^2$', fontsize=10)
    Line1.set_ylabel(r'$\ln(q^4I)$', fontsize=10)
    Line1.set_title('Porod Correction', fontsize=12)
    orig_line, = Line1.plot(A[:, 0], A[:, 1],
                            'k-', lw=1.5, label='Original Data')
    B_initial = A[:, 1] - k * A[:, 0]
    corr_line, = Line1.plot(A[:, 0], B_initial, 'r-', lw=1.5,
                            label=f'Corrected (k={k[0]:.2f})')
    Line1.legend(loc='upper left', fontsize=8)
    plt.tight_layout()
    plt.draw()
    plt.pause(0.1)
    print(f'程序给出的Porod校正系数为: {k[0]:.2f}')

    kk = k
    for _ in range(100):
        a = input('\n校正完成输入1:')
        if a == '1':
            new_B = A[:, 1] - kk * A[:, 0]
            break
        else:
            try:
                kk = float(input('\n输入Porod校正系数:'))
                new_B = A[:, 1] - kk * A[:, 0]
                corr_line.set_ydata(new_B)
                corr_line.set_label(f'Corrected (k={kk:.3f})')
                Line1.legend()
                F1.canvas.flush_events()
                F1.canvas.draw()
                plt.pause(0.01)
            except ValueError:
                print("错误：请输入数字")
    plt.ioff()  # 关闭交互模式
    plt.show()
    I_porod = np.column_stack((I_input[:, 0],
                               np.exp(new_B - 4 * np.log(I_input[:, 0])),
                               new_B - 4 * np.log(I_input[:, 0])))

    plt.ion()
    F2 = plt.figure(2, figsize=(10, 10))
    Line2 = F2.add_subplot(111)
    Line2.set_xlabel(r'$q\ (1/\mathrm{nm})$', fontsize=10)
    Line2.set_ylabel(r'$ I (a.u.) $', fontsize=10)
    Line2.set_title('log-log plot', fontsize=12)
    Line2.loglog(I_input[:, 0], I_input[:, 1], 'k-', lw=1.5)
    Line2.loglog(I_porod[:, 0], I_porod[:, 1], 'b-', lw=1.5)
    plt.tight_layout()
    plt.draw()
    F2.canvas.flush_events()
    F2.canvas.draw()
    plt.pause(0.1)
    plt.ioff()
    plt.show()
    return I_porod, kk


# %% cut_Iq
def cut_Iq(I_porod):
    plt.close('all')
    A0 = np.column_stack((np.log(I_porod[:, 0]), I_porod[:, 2]))
    plt.ion()
    F3 = plt.figure(3, figsize=(10, 10))
    Line3 = F3.add_subplot(111)
    Line3.set_xlabel('lnq', fontsize=20)
    Line3.set_ylabel('lnI', fontsize=20)
    Line3.set_title('double log plot', fontsize=12)
    Line3.plot(A0[:, 0], A0[:, 1], 'k-', lw=2)
    plt.tight_layout()
    plt.draw()
    plt.pause(0.01)
    for _ in range(100):
        a = float(input('\n输入截断的上限:'))
        k = np.argmin(np.abs(A0[:, 0] - a))
        Line3.plot(A0[0:k, 0], A0[0:k, 1], lw=2)
        F3.canvas.flush_events()
        F3.canvas.draw()
        plt.pause(0.01)

        b = input('\n截断完成请输入1:')
        if b == '1':
            break
    plt.ioff()  # 关闭交互模式
    plt.show()
    A1 = np.column_stack((np.exp(A0[0:k, 0]), np.exp(A0[0:k, 1]), A0[0:k, 1]))
    return A1


# %% 计算所有的Beta尺寸分布
def SESDC_single(swarm, I_input):
    Beta_number = 201
    Beta_R = np.zeros((swarm.shape[0], Beta_number))
    Beta_Y = np.zeros((swarm.shape[0], Beta_number))
    # 遍历每个种群
    for i in range(swarm.shape[0]):
        Y1 = get_beta(0, swarm[i, 2], swarm[i, 0], swarm[i, 1], Beta_number)  
        Beta_R[i, :], Beta_Y[i, :] = Y1[:, 0].T, Y1[:, 1].T
    
    gpu_R, gpu_Y = cp.array(Beta_R)[:, :, np.newaxis], cp.array(Beta_Y)[:, :, np.newaxis]
    gpu_V_phi = (4*cp.pi*gpu_R**3/3) * gpu_Y
    
    count_number = 100
    q = I_input[:, 0].reshape((1, 1, I_input.shape[0]))
    n = np.arange(0, I_input.shape[0], count_number)
    n = np.append(n, I_input.shape[0])
    
    # 2.GPU计算散射强度
    I2 = []
    for i in range(n.shape[0]-1):
        U1 = np.tile(q[0, 0, n[i]:n[i+1]], (gpu_Y.shape[0], gpu_Y.shape[1], 1))
        U1 = cp.array(U1) * gpu_R
        U1 = ( (3/(U1**3)) * (cp.sin(U1)-U1*cp.cos(U1)))**2 * gpu_V_phi
        I1 = cp.sum(U1, axis=1)
        I2.append(I1)
    
    I3 = cp.asnumpy(cp.concatenate(I2, axis=1))/Beta_number  # 应该计算平均值
    std_error = np.std(np.log(I3) - I_input[:, 2].T, axis=1, keepdims=True)
    
    min_index = np.argmin(std_error)
    I4 = np.column_stack((I_input[:, 0], I3[min_index,:], np.log(I3[min_index,:])))
    diff_I = np.mean(I_input[:, 2] - I4[:, 2])
    
    I4[:, 2] = I4[:, 2] + diff_I
    I4[:, 1] = np.exp(I4[:, 2])
    
    # 3.输出最终的结果
    best_swarm = swarm[min_index, :]
    coeff_phi = np.exp(diff_I)
    best_distri = np.column_stack((Beta_R[min_index, :], coeff_phi * Beta_Y[min_index, :]))
    min_std_error = std_error[min_index]
    
    return I4, best_distri, best_swarm, coeff_phi, std_error, min_std_error


# %% rotation of crystal lattice
def rotate_function_crystal(r_cal,rotate_angle):
    c,s = np.cos(rotate_angle),np.sin(rotate_angle)
    rotate_matrix_x = np.array([[1,0,0],[0,c,-s],[0,s,c]])
    rotate_matrix_y = np.array([[c,0,s],[0,1,0],[-s,0,c]])
    rotate_matrix_z = np.array([[c,-s,0],[s,c,0],[0,0,1]])
    r_cal_rotate_x = np.dot(r_cal.T,rotate_matrix_x.T).T
    r_cal_rotate_y = np.dot(r_cal.T,rotate_matrix_y.T).T
    r_cal_rotate_z = np.dot(r_cal.T,rotate_matrix_z.T).T
    return r_cal_rotate_x, r_cal_rotate_y, r_cal_rotate_z


# %% 计算向量A绕着向量B旋转
def rotateVector(A, B, alpha):
    alpha = alpha*np.pi/180
    A, B = A/np.linalg.norm(A), B/np.linalg.norm(B)
    dot_BA, cross_BA = np.dot(B, A), np.cross(B, A)
    A_rotate = A*np.cos(alpha) + cross_BA*np.sin(alpha) + B*(dot_BA*(1-np.cos(alpha)))
    A_rotate = A_rotate/np.linalg.norm(A_rotate)
    return A_rotate


# %% 批处理函数
def TwoDpattern_batch_son(pathName,current_filename,default_parameter,panduan):
    # 读取默认参数
    I_min = default_parameter[3, 0]; I_max = default_parameter[3, 1]; 
    incident_angle = default_parameter[2, 1]; 
    Center_y = default_parameter[0, 0]; Center_z = default_parameter[0, 1]; 
    pixel = default_parameter[1, 1]; distance = default_parameter[1, 0]
    lambda_Xray = default_parameter[2, 0]; qxy1_min = default_parameter[4,0];
    qxy1_max = default_parameter[4,1]; qz1_min = default_parameter[4,2]; qz1_max = default_parameter[4,3];
    qy2_min = default_parameter[5,0]; qy2_max = default_parameter[5,1];
    qz2_min = default_parameter[5,2]; qz2_max = default_parameter[5,3];
    alpha = default_parameter[0,2]; alpha_range = default_parameter[0,3];
    q_a = default_parameter[1,2]; range_q = default_parameter[1,3];
    
    # 功能一：读取二维    
    fileName = current_filename
    open_name = os.path.join(pathName, current_filename);
    pattern0 = np.array(Image.open(open_name))
    Y, Z = np.meshgrid(arange(0,1,pattern0.shape[1]-1), arange(pattern0.shape[0]-1,-1,0))
    
    # 功能二：图像翻转
    if (panduan == 1):
        pattern = np.flip(pattern0, axis=0)
    else:
        pattern = pattern0.copy()            
    
    # 功能三：绘制以pixel为自变量的普通二维图
    fig1, ax1 = plt.subplots(figsize=(10, 8))
    fig1.canvas.manager.set_window_title('2D pixel Color Map')
    pic1 = ax1.pcolormesh(Y, Z, pattern, shading='auto', cmap='jet')
    fig1.colorbar(pic1, ax=ax1, pad=0.02)
    ax1.set_xlabel('Y(pixel)', fontsize=12)
    ax1.set_ylabel('Z(pixel)', fontsize=12)
    ax1.set_aspect('equal')
    ax1.set_xlim([np.min(Y[0,:]), np.max(Y[0,:])])
    ax1.set_ylim([np.min(Z[:,0]), np.max(Z[:,0])])
    ax1.set_title(fileName)
    pic1.set_clim(I_min, I_max)
    pic1.set_cmap("jet")
    
    # 功能四：参数标定
    v_I0 = [-1, 0, 0]; v_rotate_axis = [0, -1, 0];
    v_I_GIXRS = rotateVector(v_I0, v_rotate_axis, incident_angle);
    ls_Y = (Y-Center_y)*pixel; ls_Z = (Z-Center_z)*pixel;
    ls_X=-distance*np.ones_like(ls_Y);
    norm_detector = np.sqrt(ls_X**2 + ls_Y**2 + ls_Z**2);
    vx = ls_X/norm_detector; vy = ls_Y/norm_detector; vz = ls_Z/norm_detector;
    sx = vx-v_I_GIXRS[0]; sy = vy-v_I_GIXRS[1]; sz = vz-v_I_GIXRS[2];
    # % qxy-qz模块
    qx=(2*np.pi/lambda_Xray)*sx; qy=(2*np.pi/lambda_Xray)*sy; qz=(2*np.pi/lambda_Xray)*sz; 
    q = np.sqrt(qx**2+qy**2+qz**2); qxy1 = np.sqrt(qx**2+qy**2); 
    qxy1[qy<0] = -qxy1[qy<0]; qz1 = qz;
    # % qy2-qz2模块
    norm_YZ = np.sqrt(ls_Y**2 + ls_Z**2); phi = np.acos(ls_Y/norm_YZ)*180/np.pi;
    phi[qz<0] = 360-phi[qz<0]; phi[np.isnan(phi)]=0;
    qy2 = q*np.cos(phi*np.pi/180); qz2 = q*np.sin(phi*np.pi/180);
    # % 构造GIXRS劈裂数据：qxy1-qz1
    p1=np.where(qxy1[0,:] < 0)[0][-1]; p2=np.where(qxy1[0,:] > 0)[0][0];
    add_qxy = np.column_stack([qxy1[:,p1]+1e-3, np.zeros_like(qxy1[:,p1]), qxy1[:,p2]-1e-3]);
    add_qz = np.column_stack([qz1[:,p1], 0.5*(qz1[:,p1]+qz1[:,p2]), qz1[:,p2]]);
    add_pattern = np.ones_like(add_qz)*np.min(pattern);
    qxy1 = np.column_stack([qxy1[:,0:p1+1],add_qxy,qxy1[:,p2:]]);
    qz1 = np.column_stack([qz1[:,0:p1+1],add_qz,qz1[:,p2:]]);
    pattern1 = np.column_stack([pattern[:,0:p1+1],add_pattern,pattern[:,p2:]]);
    
    # 功能五：绘制劈裂图
    fig2, ax2 = plt.subplots(figsize=(10, 8));
    fig2.canvas.manager.set_window_title('qxy-qz Color Map with missing wedge');
    pic2 = ax2.pcolormesh(qxy1, qz1, pattern1, shading='auto', cmap='jet');
    fig2.colorbar(pic2, ax=ax2, pad=0.02);
    ax2.set_xlabel(r'$q\mathregular{_{xy}\/(\AA^{-1}})$', family="Arial", fontsize=12); 
    ax2.set_ylabel(r'$q\mathregular{_{z}\/(\AA^{-1}})$', family="Arial", fontsize=12);
    ax2.set_aspect('equal');
    ax2.set_xlim([np.min(qxy1[0,:]), np.max(qxy1[0,:])]);
    ax2.set_ylim([np.min(qz1[:,0]), np.max(qz1[:,0])]);
    ax2.set_title(fileName);    
    pic2.set_cmap("jet");
    ax2.set_xlim([qxy1_min, qxy1_max]);
    ax2.set_ylim([qz1_min, qz1_max]);
    pic2.set_clim(I_min, I_max);
    
    # 功能六：绘制常规Q空间二维图
    fig3, ax3 = plt.subplots(figsize=(10, 8));
    fig3.canvas.manager.set_window_title('qy-qz Color Map');
    pic3 = ax3.pcolormesh(qy2, qz2, pattern, shading='auto', cmap='jet');
    fig3.colorbar(pic3, ax=ax3, pad=0.02);
    ax3.set_xlabel(r'$q\mathregular{_{y}\/(\AA^{-1}})$', family="Arial", fontsize=12); 
    ax3.set_ylabel(r'$q\mathregular{_{z}\/(\AA^{-1}})$', family="Arial", fontsize=12);
    ax3.set_aspect('equal');    
    ax3.set_title(fileName);    
    pic3.set_cmap("jet");
    ax3.set_xlim([qy2_min, qy2_max]);
    ax3.set_ylim([qz2_min, qz2_max]);
    pic3.set_clim(I_min, I_max);
    
    # 功能七：径向积分I-q曲线    
    t1=np.abs(phi-alpha); t1[t1>alpha_range]=100;
    I1=np.column_stack([q[t1<alpha_range],pattern[t1<alpha_range]]);
    I1 = I1[np.argsort(I1[:,0])] # 按照第一列排序
    L1 = norm_YZ[t1<alpha_range]; length_L = int(np.floor(np.max(L1)/pixel));
    q0 = np.linspace(np.min(I1[:,0]),np.max(I1[:,0]),length_L)
    stat, bin_edges, _ = binned_statistic(I1[:, 0], I1[:, 1], statistic='mean', bins=q0)
    Im = np.column_stack([bin_edges[:-1],stat])
    Im = Im[~np.isnan(Im[:,1])];
    fig4, ax4 = plt.subplots(figsize=(10, 8));
    fig4.canvas.manager.set_window_title('1D I-q integration');
    pic4 = ax4.plot(Im[:,0],Im[:,1]); 
    pic4[0].set_color('red'); pic4[0].set_linewidth(3);
    ax4.set_xlabel(r'$q\mathregular{\/(\AA^{-1}})$', family="Arial", fontsize=12, weight='bold');
    ax4.set_ylabel('I (a.u.)', family="Arial", fontsize=12, weight='bold');
    ax4.set_title(fileName, weight='bold');
    
    # 功能八：方位角积分I-φ曲线    
    azimuthal_angle=np.linspace(0,360,361);
    t1=np.abs(q-q_a); t1[t1>range_q]=100;
    I1=np.column_stack([phi[t1<1],pattern[t1<1]]);
    I1 = I1[np.argsort(I1[:,0])] # 按照第一列排序
    stat, bin_edges, _ = binned_statistic(I1[:, 0], I1[:, 1], statistic='mean', bins=azimuthal_angle);
    In = np.column_stack([bin_edges[:-1],stat]);
    In = In[~np.isnan(In[:,1])];
    fig5, ax5 = plt.subplots(figsize=(10, 8));
    fig5.canvas.manager.set_window_title('1D I-phi azimuthal scan');
    pic5 = ax5.plot(In[:,0],In[:,1]); 
    pic5[0].set_color('blue'); pic5[0].set_linewidth(3);
    ax5.set_xlabel('phi (deg)', family="Arial", fontsize=12, weight='bold');
    ax5.set_ylabel('I (a.u.)', family="Arial", fontsize=12, weight='bold');
    ax5.set_title(fileName, weight='bold');
    
    # 功能九：计算结果保存
    baseName = os.path.splitext(fileName)[0]
    analysis_folder = os.path.join(pathName,f"{baseName}_analysis")
    os.makedirs(analysis_folder, exist_ok=True)
    shutil.copy2(os.path.join(pathName,fileName), analysis_folder)

    fig1.savefig(os.path.join(analysis_folder,f"{baseName}_pixel_2D_pattern.png"), dpi=200, bbox_inches='tight')
    fig2.savefig(os.path.join(analysis_folder,f"{baseName}_qz-qxy_2D_pattern.png"), dpi=200, bbox_inches='tight')
    fig3.savefig(os.path.join(analysis_folder,f"{baseName}_qz-qy_2D_pattern.png"), dpi=200, bbox_inches='tight')
    fig4.savefig(os.path.join(analysis_folder,f"{baseName}_I-q_1D_integration.png"), dpi=200, bbox_inches='tight')
    fig5.savefig(os.path.join(analysis_folder,f"{baseName}_I-phi_1D_integration.png"), dpi=200, bbox_inches='tight')
    np.savetxt(os.path.join(analysis_folder,f"{baseName}_I-q_data.txt"), Im, fmt='%.6f', delimiter='\t')
    np.savetxt(os.path.join(analysis_folder,f"{baseName}_I-phi_data.txt"), In, fmt='%.6f', delimiter='\t')
    
    plt.close('all')



