# Requirements for <PERSON><PERSON>_<PERSON>_2D_pattern_V2.py
# Core scientific computing libraries
numpy>=1.21.0
scipy>=1.7.0

# Image processing
Pillow>=8.0.0

# GUI framework
PyQt5>=5.15.0

# Plotting and visualization
matplotlib>=3.5.0

# Custom module (if available via pip, otherwise install manually)
# SAXS_CRC

# Note: tkinter is included with Python standard library, no separate installation needed
